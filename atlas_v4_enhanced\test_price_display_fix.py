#!/usr/bin/env python3
"""
Test script to verify Lee Method Scanner price display fixes
Tests that prices are properly included in signals and displayed in UI
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner
from atlas_market_core import AtlasMarketEngine
from atlas_orchestrator import AtlasOrchestrator

class PriceDisplayTester:
    def __init__(self):
        self.market_engine = AtlasMarketEngine()
        self.lee_scanner = LeeMethodScanner(market_engine=self.market_engine)
        self.realtime_scanner = AtlasLeeMethodRealtimeScanner(market_engine=self.market_engine)
        self.orchestrator = AtlasOrchestrator()

    async def test_signal_price_inclusion(self):
        """Test that signals include current price data"""
        print("\n💰 TESTING SIGNAL PRICE INCLUSION")
        print("="*50)
        
        test_symbols = ["AAPL", "MSFT", "TSLA"]
        results = {}
        
        for symbol in test_symbols:
            print(f"\nTesting {symbol}:")
            
            try:
                # Test individual signal generation
                signal = await self.lee_scanner.scan_symbol(symbol)
                
                if signal:
                    print(f"  ✅ Signal Generated: {signal.signal_type}")
                    print(f"     Current Price: ${signal.current_price:.2f}")
                    print(f"     Price Change: {signal.price_change:+.2f}")
                    print(f"     Price Change %: {signal.price_change_percent:+.2f}%")
                    print(f"     Entry Price: ${signal.entry_price:.2f}")
                    print(f"     Target Price: ${signal.target_price:.2f}")
                    print(f"     Stop Loss: ${signal.stop_loss:.2f}")
                    
                    # Test signal dictionary conversion
                    signal_dict = signal.to_dict()
                    has_price = 'price' in signal_dict and signal_dict['price'] > 0
                    has_current_price = 'current_price' in signal_dict and signal_dict['current_price'] > 0
                    
                    print(f"     Dict has 'price' field: {has_price}")
                    print(f"     Dict has 'current_price' field: {has_current_price}")
                    
                    results[symbol] = {
                        "signal_generated": True,
                        "has_price_field": has_price,
                        "has_current_price_field": has_current_price,
                        "current_price": signal.current_price,
                        "price_change": signal.price_change,
                        "price_change_percent": signal.price_change_percent,
                        "signal_dict_price": signal_dict.get('price', 0),
                        "signal_dict_current_price": signal_dict.get('current_price', 0)
                    }
                else:
                    print(f"  ❌ No Signal Generated")
                    results[symbol] = {"signal_generated": False}
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
                results[symbol] = {"error": str(e)}
        
        return results

    async def test_orchestrator_signals(self):
        """Test that orchestrator returns signals with prices"""
        print("\n🎯 TESTING ORCHESTRATOR SIGNAL INTEGRATION")
        print("="*50)
        
        try:
            # Initialize orchestrator
            await self.orchestrator.initialize()
            
            # Test Lee Method signals through orchestrator
            signals_data = await self.orchestrator.get_lee_method_signals()
            signals = signals_data.get('signals', [])
            
            print(f"Signals returned: {len(signals)}")
            
            if signals:
                # Check first few signals for price data
                for i, signal in enumerate(signals[:3], 1):
                    symbol = signal.get('symbol', 'Unknown')
                    price = signal.get('price', 0)
                    current_price = signal.get('current_price', 0)
                    
                    print(f"  Signal {i} ({symbol}):")
                    print(f"    Price: ${price:.2f}")
                    print(f"    Current Price: ${current_price:.2f}")
                    print(f"    Has price field: {'price' in signal}")
                    print(f"    Has current_price field: {'current_price' in signal}")
                
                return {
                    "signals_count": len(signals),
                    "first_signal_has_price": 'price' in signals[0] if signals else False,
                    "first_signal_price": signals[0].get('price', 0) if signals else 0,
                    "all_signals_have_price": all('price' in s for s in signals),
                    "sample_signals": signals[:3]
                }
            else:
                print("  ❌ No signals returned")
                return {"signals_count": 0, "error": "No signals returned"}
                
        except Exception as e:
            print(f"  ❌ Orchestrator Error: {e}")
            return {"error": str(e)}

    async def test_realtime_scanner_status(self):
        """Test real-time scanner status and functionality"""
        print("\n📡 TESTING REAL-TIME SCANNER")
        print("="*50)
        
        try:
            # Test scanner status
            status = self.realtime_scanner.get_status()
            print(f"Scanner Running: {status['running']}")
            print(f"Scan Count: {status['scan_count']}")
            print(f"Active Signals: {status['active_results_count']}")
            
            # Test starting scanner
            if not status['running']:
                print("\nStarting real-time scanner...")
                await self.realtime_scanner.start_scanning()
                
                # Wait a moment for initial scan
                await asyncio.sleep(5)
                
                # Check status again
                new_status = self.realtime_scanner.get_status()
                print(f"Scanner now running: {new_status['running']}")
                print(f"Scan count after start: {new_status['scan_count']}")
                
                # Stop scanner
                await self.realtime_scanner.stop_scanning()
                print("Scanner stopped")
            
            return status
            
        except Exception as e:
            print(f"  ❌ Real-time Scanner Error: {e}")
            return {"error": str(e)}

    async def test_market_data_integration(self):
        """Test market data integration"""
        print("\n📊 TESTING MARKET DATA INTEGRATION")
        print("="*50)
        
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        results = {}
        
        try:
            await self.market_engine.initialize()
            
            for symbol in test_symbols:
                print(f"\nTesting {symbol}:")
                
                # Test direct market engine
                quote = await self.market_engine.get_quote(symbol)
                if quote:
                    print(f"  ✅ Market Engine: ${quote.price:.2f} ({quote.change:+.2f})")
                    results[symbol] = {
                        "market_engine_working": True,
                        "price": quote.price,
                        "change": quote.change,
                        "change_percent": quote.change_percent
                    }
                else:
                    print(f"  ❌ Market Engine: No data")
                    results[symbol] = {"market_engine_working": False}
            
            return results
            
        except Exception as e:
            print(f"  ❌ Market Data Error: {e}")
            return {"error": str(e)}

async def main():
    """Run comprehensive price display testing"""
    print("🔧 LEE METHOD SCANNER PRICE DISPLAY FIX TEST")
    print("="*60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    tester = PriceDisplayTester()
    
    # Initialize components
    print("Initializing components...")
    await tester.market_engine.initialize()
    await tester.lee_scanner.initialize()
    
    # Run all tests
    results = {}
    
    try:
        results["signal_price_inclusion"] = await tester.test_signal_price_inclusion()
        results["orchestrator_signals"] = await tester.test_orchestrator_signals()
        results["realtime_scanner"] = await tester.test_realtime_scanner_status()
        results["market_data_integration"] = await tester.test_market_data_integration()
        
    except Exception as e:
        print(f"❌ Test Error: {e}")
        results["error"] = str(e)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"price_display_fix_test_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved to: {filename}")
    
    # Summary
    print(f"\n📋 TEST SUMMARY")
    print("="*30)
    
    signal_tests = results.get("signal_price_inclusion", {})
    working_signals = sum(1 for r in signal_tests.values() if isinstance(r, dict) and r.get("has_price_field"))
    total_signals = len([r for r in signal_tests.values() if isinstance(r, dict) and "signal_generated" in r])
    
    orchestrator_test = results.get("orchestrator_signals", {})
    orchestrator_working = orchestrator_test.get("all_signals_have_price", False)
    
    print(f"✅ Signals with price data: {working_signals}/{total_signals}")
    print(f"✅ Orchestrator signals working: {orchestrator_working}")
    print(f"✅ Market data integration: {'Working' if results.get('market_data_integration') else 'Failed'}")

if __name__ == "__main__":
    asyncio.run(main())
