#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Chatbot Testing Suite
Tests all major chatbot functionality including AI integration, response quality, and performance
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AtlasChatbotTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.test_results = []
        self.session_id = f"test_session_{int(time.time())}"
        
    async def send_message(self, message: str, timeout: int = 30) -> Dict[str, Any]:
        """Send message to A.T.L.A.S. chatbot and measure response time"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "message": message,
                    "session_id": self.session_id,
                    "user_id": "test_user"
                }
                
                async with session.post(
                    f"{self.base_url}/api/v1/chat",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "response": data.get("response", ""),
                            "type": data.get("type", "unknown"),
                            "confidence": data.get("confidence", 0.0),
                            "context": data.get("context", {}),
                            "response_time": response_time,
                            "status_code": response.status
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "response_time": response_time,
                            "status_code": response.status
                        }
                        
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "response_time": response_time,
                "status_code": None
            }

    def evaluate_response(self, query: str, response_data: Dict[str, Any], expected_type: str = None) -> Dict[str, Any]:
        """Evaluate response quality and characteristics"""
        if not response_data.get("success"):
            return {
                "accuracy": 0.0,
                "relevance": 0.0,
                "completeness": 0.0,
                "safety": 0.0,
                "performance": 0.0,
                "overall_score": 0.0,
                "issues": [f"Request failed: {response_data.get('error', 'Unknown error')}"]
            }
        
        response_text = response_data.get("response", "")
        response_time = response_data.get("response_time", 0)
        confidence = response_data.get("confidence", 0)
        
        issues = []
        
        # Accuracy assessment (based on confidence and response quality)
        accuracy = min(confidence * 100, 95) if confidence > 0 else 50
        
        # Relevance assessment (keyword matching and response appropriateness)
        relevance = self._assess_relevance(query, response_text)
        
        # Completeness assessment (response length and detail)
        completeness = self._assess_completeness(response_text)
        
        # Safety assessment (disclaimers and risk warnings)
        safety = self._assess_safety(response_text)
        
        # Performance assessment (response time)
        performance = self._assess_performance(response_time)
        
        # Overall score
        overall_score = (accuracy + relevance + completeness + safety + performance) / 5
        
        return {
            "accuracy": accuracy,
            "relevance": relevance,
            "completeness": completeness,
            "safety": safety,
            "performance": performance,
            "overall_score": overall_score,
            "response_time": response_time,
            "confidence": confidence,
            "issues": issues
        }
    
    def _assess_relevance(self, query: str, response: str) -> float:
        """Assess how relevant the response is to the query"""
        query_lower = query.lower()
        response_lower = response.lower()
        
        # Extract key terms from query
        financial_terms = ['stock', 'price', 'buy', 'sell', 'trade', 'invest', 'market', 'analysis']
        technical_terms = ['rsi', 'macd', 'support', 'resistance', 'volume', 'trend']
        
        query_terms = set(query_lower.split())
        response_terms = set(response_lower.split())
        
        # Check for direct term matches
        direct_matches = len(query_terms.intersection(response_terms))
        total_query_terms = len(query_terms)
        
        if total_query_terms == 0:
            return 50.0
        
        relevance_score = (direct_matches / total_query_terms) * 100
        
        # Bonus for financial context
        if any(term in response_lower for term in financial_terms):
            relevance_score = min(relevance_score + 20, 100)
            
        return max(relevance_score, 30)  # Minimum 30% relevance
    
    def _assess_completeness(self, response: str) -> float:
        """Assess completeness of the response"""
        if len(response) < 50:
            return 20.0
        elif len(response) < 150:
            return 60.0
        elif len(response) < 300:
            return 80.0
        else:
            return 95.0
    
    def _assess_safety(self, response: str) -> float:
        """Assess safety disclaimers and risk warnings"""
        response_lower = response.lower()
        
        safety_indicators = [
            'disclaimer', 'risk', 'not financial advice', 'consult', 'professional',
            'past performance', 'no guarantee', 'investment risk', 'do your own research'
        ]
        
        safety_score = 0
        for indicator in safety_indicators:
            if indicator in response_lower:
                safety_score += 15
        
        # Financial advice detection (should be avoided)
        direct_advice = ['you should buy', 'you should sell', 'i recommend buying', 'i recommend selling']
        for advice in direct_advice:
            if advice in response_lower:
                safety_score -= 30
        
        return max(min(safety_score, 100), 0)
    
    def _assess_performance(self, response_time: float) -> float:
        """Assess response time performance"""
        if response_time < 2.0:
            return 100.0
        elif response_time < 5.0:
            return 80.0
        elif response_time < 10.0:
            return 60.0
        elif response_time < 20.0:
            return 40.0
        else:
            return 20.0

    async def run_test_suite(self):
        """Run comprehensive test suite"""
        logger.info("Starting A.T.L.A.S. Chatbot Comprehensive Testing")
        
        # Test categories with specific queries
        test_categories = {
            "Market Analysis": [
                "Analyze AAPL stock",
                "What's the sentiment for tech stocks?",
                "Give me a market analysis for Tesla",
                "How is the semiconductor sector performing?",
                "Analyze the current market trends"
            ],
            "Trading Strategy": [
                "Should I buy or sell TSLA?",
                "What's your recommendation for SPY?",
                "Is now a good time to invest in NVDA?",
                "Give me a trading strategy for volatile markets",
                "What do you think about buying Microsoft?"
            ],
            "Technical Analysis": [
                "Show me RSI for NVDA",
                "What are the support levels for QQQ?",
                "Calculate MACD for Apple stock",
                "What's the moving average for Amazon?",
                "Analyze the volume trends for Google"
            ],
            "General Market": [
                "What's happening in the market today?",
                "Explain this earnings report",
                "What are the major market movers?",
                "How do interest rates affect stocks?",
                "What's the outlook for the economy?"
            ],
            "AI Integration": [
                "Use advanced AI to analyze the market",
                "What does Grok think about current market conditions?",
                "Give me an AI-powered investment insight",
                "Use machine learning to predict stock movements",
                "What's your most advanced analysis capability?"
            ]
        }
        
        for category, queries in test_categories.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"Testing Category: {category}")
            logger.info(f"{'='*50}")
            
            category_results = []
            
            for i, query in enumerate(queries, 1):
                logger.info(f"\nTest {i}/{len(queries)}: {query}")
                
                # Send query and get response
                response_data = await self.send_message(query)
                
                # Evaluate response
                evaluation = self.evaluate_response(query, response_data)
                
                # Store results
                test_result = {
                    "category": category,
                    "query": query,
                    "response_data": response_data,
                    "evaluation": evaluation,
                    "timestamp": datetime.now().isoformat()
                }
                
                category_results.append(test_result)
                self.test_results.append(test_result)
                
                # Log results
                if response_data.get("success"):
                    logger.info(f"✅ Response received ({evaluation['response_time']:.2f}s)")
                    logger.info(f"📊 Overall Score: {evaluation['overall_score']:.1f}/100")
                    logger.info(f"🎯 Accuracy: {evaluation['accuracy']:.1f}% | Relevance: {evaluation['relevance']:.1f}%")
                    logger.info(f"📝 Completeness: {evaluation['completeness']:.1f}% | Safety: {evaluation['safety']:.1f}%")
                    logger.info(f"⚡ Performance: {evaluation['performance']:.1f}%")
                    
                    # Show response preview
                    response_preview = response_data.get("response", "")[:200]
                    logger.info(f"💬 Response: {response_preview}...")
                else:
                    logger.error(f"❌ Test failed: {response_data.get('error', 'Unknown error')}")
                
                # Brief pause between requests
                await asyncio.sleep(1)
            
            # Category summary
            if category_results:
                avg_score = sum(r["evaluation"]["overall_score"] for r in category_results) / len(category_results)
                avg_time = sum(r["response_data"]["response_time"] for r in category_results if r["response_data"].get("response_time")) / len(category_results)
                success_rate = sum(1 for r in category_results if r["response_data"].get("success")) / len(category_results) * 100
                
                logger.info(f"\n📈 {category} Summary:")
                logger.info(f"   Average Score: {avg_score:.1f}/100")
                logger.info(f"   Average Response Time: {avg_time:.2f}s")
                logger.info(f"   Success Rate: {success_rate:.1f}%")
        
        return self.test_results

if __name__ == "__main__":
    async def main():
        tester = AtlasChatbotTester()
        results = await tester.run_test_suite()
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"atlas_chatbot_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n🎉 Testing completed! Results saved to {filename}")
    
    asyncio.run(main())
