#!/usr/bin/env python3
"""
Lee Method Scanner Investigation & Testing Script
Tests market hours detection, data sources, and pattern generation
"""

import asyncio
import json
import sys
import os
from datetime import datetime, time
from typing import Dict, List, Any
import pytz

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner
from atlas_realtime_scanner import AtlasRealtimeScanner
from atlas_market_core import AtlasMarketEngine

class LeeMethodInvestigator:
    def __init__(self):
        self.lee_scanner = LeeMethodScanner()
        self.realtime_scanner = AtlasRealtimeScanner()
        self.market_engine = AtlasMarketEngine()
        
    async def test_market_hours_detection(self):
        """Test market hours detection logic"""
        print("🕐 TESTING MARKET HOURS DETECTION")
        print("="*50)
        
        # Current time analysis
        now_local = datetime.now()
        et_tz = pytz.timezone('US/Eastern')
        now_et = datetime.now(et_tz)
        
        print(f"Current Local Time: {now_local.strftime('%H:%M:%S %Z')}")
        print(f"Current Eastern Time: {now_et.strftime('%H:%M:%S %Z')}")
        
        # Market hours
        market_open = time(9, 30)
        market_close = time(16, 0)
        
        print(f"Market Hours: {market_open} - {market_close} ET")
        
        # Test current logic (BROKEN)
        current_time_local = now_local.time()
        is_market_hours_broken = market_open <= current_time_local <= market_close
        
        # Test correct logic (FIXED)
        current_time_et = now_et.time()
        is_market_hours_correct = market_open <= current_time_et <= market_close
        
        print(f"\n❌ BROKEN Logic (Local Time): {is_market_hours_broken}")
        print(f"✅ CORRECT Logic (ET Time): {is_market_hours_correct}")
        
        # Test scanner's current logic
        scanner_should_scan = self.realtime_scanner._should_scan()
        print(f"🔍 Scanner _should_scan(): {scanner_should_scan}")
        
        return {
            "local_time": now_local.isoformat(),
            "et_time": now_et.isoformat(),
            "market_hours_broken": is_market_hours_broken,
            "market_hours_correct": is_market_hours_correct,
            "scanner_should_scan": scanner_should_scan
        }
    
    async def test_data_sources(self):
        """Test real-time data sources"""
        print("\n📊 TESTING DATA SOURCES")
        print("="*50)
        
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        results = {}
        
        for symbol in test_symbols:
            print(f"\nTesting {symbol}:")
            
            # Test market engine
            try:
                quote = await self.market_engine.get_quote(symbol)
                if quote:
                    print(f"  ✅ Market Engine: ${quote.price:.2f} ({quote.change:+.2f})")
                    results[symbol] = {
                        "market_engine": True,
                        "price": quote.price,
                        "change": quote.change
                    }
                else:
                    print(f"  ❌ Market Engine: No data")
                    results[symbol] = {"market_engine": False}
            except Exception as e:
                print(f"  ❌ Market Engine Error: {e}")
                results[symbol] = {"market_engine": False, "error": str(e)}
            
            # Test historical data
            try:
                df = await self.lee_scanner.fetch_historical_data(symbol, limit=10)
                if not df.empty:
                    latest_close = df['close'].iloc[-1]
                    latest_date = df['date'].iloc[-1]
                    print(f"  ✅ Historical Data: ${latest_close:.2f} ({latest_date})")
                    results[symbol]["historical_data"] = True
                    results[symbol]["latest_close"] = latest_close
                    results[symbol]["latest_date"] = str(latest_date)
                else:
                    print(f"  ❌ Historical Data: Empty")
                    results[symbol]["historical_data"] = False
            except Exception as e:
                print(f"  ❌ Historical Data Error: {e}")
                results[symbol]["historical_data"] = False
        
        return results
    
    async def test_pattern_detection(self):
        """Test pattern detection logic"""
        print("\n🔍 TESTING PATTERN DETECTION")
        print("="*50)
        
        test_symbols = ["AAPL", "MSFT", "TSLA"]
        results = {}
        
        for symbol in test_symbols:
            print(f"\nTesting pattern detection for {symbol}:")
            
            try:
                # Test with current (flexible) settings
                signal = await self.lee_scanner.scan_symbol(symbol)
                
                if signal:
                    print(f"  ✅ Pattern Found: {signal.signal_type}")
                    print(f"     Confidence: {signal.confidence:.2f}")
                    print(f"     Strength: {signal.strength}")
                    print(f"     Entry: ${signal.entry_price:.2f}")
                    print(f"     Target: ${signal.target_price:.2f}")
                    print(f"     Stop: ${signal.stop_loss:.2f}")
                    
                    results[symbol] = {
                        "pattern_found": True,
                        "signal_type": signal.signal_type,
                        "confidence": signal.confidence,
                        "strength": signal.strength,
                        "entry_price": signal.entry_price,
                        "target_price": signal.target_price,
                        "stop_loss": signal.stop_loss
                    }
                else:
                    print(f"  ❌ No Pattern Found")
                    results[symbol] = {"pattern_found": False}
                    
            except Exception as e:
                print(f"  ❌ Pattern Detection Error: {e}")
                results[symbol] = {"pattern_found": False, "error": str(e)}
        
        return results
    
    async def test_pattern_sensitivity(self):
        """Test different pattern sensitivity settings"""
        print("\n⚙️ TESTING PATTERN SENSITIVITY")
        print("="*50)
        
        test_symbol = "AAPL"
        sensitivity_tests = [
            {"name": "Current (Flexible)", "flexible": True, "min_conf": 0.4, "sensitivity": 0.7, "weak": True},
            {"name": "Moderate", "flexible": True, "min_conf": 0.6, "sensitivity": 0.5, "weak": False},
            {"name": "Strict", "flexible": False, "min_conf": 0.7, "sensitivity": 0.3, "weak": False}
        ]
        
        results = {}
        
        for test in sensitivity_tests:
            print(f"\nTesting {test['name']} settings:")
            
            # Configure scanner
            self.lee_scanner.configure_pattern_sensitivity(
                use_flexible_patterns=test['flexible'],
                min_confidence_threshold=test['min_conf'],
                pattern_sensitivity=test['sensitivity'],
                allow_weak_signals=test['weak']
            )
            
            try:
                signal = await self.lee_scanner.scan_symbol(test_symbol)
                
                if signal:
                    print(f"  ✅ Pattern: {signal.confidence:.2f} confidence, {signal.strength} strength")
                    results[test['name']] = {
                        "pattern_found": True,
                        "confidence": signal.confidence,
                        "strength": signal.strength
                    }
                else:
                    print(f"  ❌ No pattern found")
                    results[test['name']] = {"pattern_found": False}
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
                results[test['name']] = {"error": str(e)}
        
        return results
    
    async def test_scanner_status(self):
        """Test real-time scanner status"""
        print("\n📡 TESTING SCANNER STATUS")
        print("="*50)
        
        try:
            status = self.realtime_scanner.get_status()
            
            print(f"Scanner Running: {status['running']}")
            print(f"Market Hours: {status['market_hours']}")
            print(f"Scan Count: {status['scan_count']}")
            print(f"Active Results: {status['active_results_count']}")
            print(f"Symbols Monitored: {status['symbols_monitored']}")
            print(f"Last Scan: {status['last_scan_time']}")
            
            return status
            
        except Exception as e:
            print(f"❌ Scanner Status Error: {e}")
            return {"error": str(e)}

async def main():
    """Run comprehensive Lee Method scanner investigation"""
    print("🔍 A.T.L.A.S. LEE METHOD SCANNER INVESTIGATION")
    print("="*60)
    print(f"Investigation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    investigator = LeeMethodInvestigator()
    
    # Initialize components
    print("Initializing components...")
    await investigator.lee_scanner.initialize()
    await investigator.market_engine.initialize()
    
    # Run all tests
    results = {}
    
    try:
        results["market_hours"] = await investigator.test_market_hours_detection()
        results["data_sources"] = await investigator.test_data_sources()
        results["pattern_detection"] = await investigator.test_pattern_detection()
        results["pattern_sensitivity"] = await investigator.test_pattern_sensitivity()
        results["scanner_status"] = await investigator.test_scanner_status()
        
    except Exception as e:
        print(f"❌ Investigation Error: {e}")
        results["error"] = str(e)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"lee_method_investigation_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Investigation results saved to: {filename}")
    
    # Summary
    print(f"\n📋 INVESTIGATION SUMMARY")
    print("="*30)
    
    market_hours_issue = not results.get("market_hours", {}).get("market_hours_correct", True)
    data_sources_working = any(
        result.get("market_engine", False) 
        for result in results.get("data_sources", {}).values()
    )
    patterns_detected = any(
        result.get("pattern_found", False) 
        for result in results.get("pattern_detection", {}).values()
    )
    
    print(f"❌ Market Hours Issue: {market_hours_issue}")
    print(f"✅ Data Sources Working: {data_sources_working}")
    print(f"⚠️  Patterns Being Generated: {patterns_detected}")
    
    if market_hours_issue:
        print("\n🚨 CRITICAL: Fix market hours detection before production!")
    
    if patterns_detected and market_hours_issue:
        print("🚨 CRITICAL: Patterns being generated outside market hours!")

if __name__ == "__main__":
    asyncio.run(main())
