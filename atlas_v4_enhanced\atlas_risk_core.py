"""
A.T.L.A.S Risk Core - Consolidated Risk Management Engine
Combines Risk Engine, Portfolio Optimizer, VaR Calculator, and Options Engine
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import get_api_config, settings
from models import EngineStatus, RiskAssessment, Position

logger = logging.getLogger(__name__)


# ============================================================================
# RISK ENGINE
# ============================================================================

class AtlasRiskEngine:
    """Consolidated risk management engine with portfolio optimization"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        
        # Risk parameters
        self.max_position_size = 0.02  # 2% max position size
        self.max_portfolio_risk = 0.20  # 20% max portfolio risk
        self.max_daily_loss = 0.05  # 5% max daily loss
        self.max_correlation = 0.7  # Max correlation between positions
        
        # Portfolio tracking
        self.positions = {}
        self.portfolio_value = 100000.0  # Starting value
        self.daily_pnl = 0.0
        self.risk_metrics = {}
        
        logger.info("[SHIELD] Risk Engine created - safety guardrails active")

    async def initialize(self):
        """Initialize risk engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Load risk parameters
            await self._load_risk_parameters()
            
            # Initialize monitoring systems
            await self._initialize_monitoring()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Risk Engine initialization completed")
            
        except Exception as e:
            logger.error(f"Risk Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_risk_parameters(self):
        """Load risk management parameters"""
        try:
            # Default risk parameters
            self.risk_parameters = {
                'max_position_size': self.max_position_size,
                'max_portfolio_risk': self.max_portfolio_risk,
                'max_daily_loss': self.max_daily_loss,
                'max_correlation': self.max_correlation,
                'var_confidence': 0.95,  # 95% VaR confidence
                'lookback_days': 252  # 1 year lookback
            }
            
            logger.info(f"[DATA] Risk parameters loaded - Max position size: {self.max_position_size*100}%")
            
        except Exception as e:
            logger.error(f"Failed to load risk parameters: {e}")
            raise

    async def _initialize_monitoring(self):
        """Initialize risk monitoring systems"""
        try:
            # Initialize risk monitoring
            self.monitoring_active = True
            self.alert_thresholds = {
                'position_size': 0.015,  # Alert at 1.5%
                'portfolio_risk': 0.15,  # Alert at 15%
                'daily_loss': 0.03  # Alert at 3%
            }
            
            logger.info("[SEARCH] Risk monitoring systems initialized")
            
        except Exception as e:
            logger.error(f"Risk monitoring initialization failed: {e}")
            raise

    async def assess_position_risk(self, symbol: str, quantity: float, price: float, 
                                 portfolio_value: float = None) -> RiskAssessment:
        """Assess risk for a potential position"""
        try:
            if portfolio_value is None:
                portfolio_value = self.portfolio_value
            
            # Calculate position value
            position_value = quantity * price
            position_size_percent = (position_value / portfolio_value) * 100
            
            # Risk assessment
            risk_level = "LOW"
            if position_size_percent > self.max_position_size * 100:
                risk_level = "HIGH"
            elif position_size_percent > self.alert_thresholds['position_size'] * 100:
                risk_level = "MEDIUM"
            
            # Calculate recommended position size
            max_position_value = portfolio_value * self.max_position_size
            recommended_quantity = int(max_position_value / price)
            
            # Risk metrics
            risk_metrics = {
                'position_size_percent': position_size_percent,
                'max_allowed_percent': self.max_position_size * 100,
                'risk_level': risk_level,
                'recommended_quantity': recommended_quantity,
                'max_loss_amount': position_value * 0.02,  # 2% stop loss
                'risk_reward_ratio': 2.0  # Default 2:1
            }
            
            return RiskAssessment(
                symbol=symbol,
                risk_level=risk_level,
                position_size_percent=position_size_percent,
                max_loss_amount=risk_metrics['max_loss_amount'],
                recommended_quantity=recommended_quantity,
                risk_metrics=risk_metrics,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Position risk assessment failed for {symbol}: {e}")
            return RiskAssessment(
                symbol=symbol,
                risk_level="HIGH",
                position_size_percent=100.0,
                max_loss_amount=0.0,
                recommended_quantity=0,
                risk_metrics={},
                timestamp=datetime.now()
            )

    async def calculate_portfolio_risk(self, positions: Dict[str, Position] = None) -> Dict[str, Any]:
        """Calculate overall portfolio risk metrics"""
        try:
            if positions is None:
                positions = self.positions
            
            if not positions:
                return {
                    'total_risk': 0.0,
                    'var_95': 0.0,
                    'max_drawdown': 0.0,
                    'concentration_risk': 0.0,
                    'correlation_risk': 0.0
                }
            
            # Calculate basic risk metrics
            total_value = sum(pos.market_value for pos in positions.values())
            largest_position = max(pos.market_value for pos in positions.values()) if positions else 0
            concentration_risk = (largest_position / total_value) * 100 if total_value > 0 else 0
            
            # Simplified VaR calculation (normally would use historical data)
            portfolio_volatility = 0.15  # Assume 15% annual volatility
            var_95 = total_value * portfolio_volatility * 2.33 / np.sqrt(252)  # Daily 95% VaR
            
            return {
                'total_value': total_value,
                'total_risk': min(concentration_risk, 100.0),
                'var_95': var_95,
                'max_drawdown': 0.0,  # Would calculate from historical data
                'concentration_risk': concentration_risk,
                'correlation_risk': 0.0,  # Would calculate from correlation matrix
                'position_count': len(positions),
                'risk_level': 'LOW' if concentration_risk < 10 else 'MEDIUM' if concentration_risk < 20 else 'HIGH'
            }
            
        except Exception as e:
            logger.error(f"Portfolio risk calculation failed: {e}")
            return {'error': str(e)}

    async def check_risk_limits(self, symbol: str, quantity: float, price: float) -> Dict[str, Any]:
        """Check if trade violates risk limits"""
        try:
            # Assess position risk
            risk_assessment = await self.assess_position_risk(symbol, quantity, price)
            
            # Check limits
            violations = []
            warnings = []
            
            if risk_assessment.position_size_percent > self.max_position_size * 100:
                violations.append(f"Position size {risk_assessment.position_size_percent:.1f}% exceeds limit {self.max_position_size*100}%")
            elif risk_assessment.position_size_percent > self.alert_thresholds['position_size'] * 100:
                warnings.append(f"Position size {risk_assessment.position_size_percent:.1f}% approaching limit")
            
            # Check daily loss limit
            if abs(self.daily_pnl) > self.max_daily_loss * self.portfolio_value:
                violations.append(f"Daily loss limit exceeded")
            
            return {
                'approved': len(violations) == 0,
                'violations': violations,
                'warnings': warnings,
                'risk_assessment': risk_assessment,
                'recommended_action': 'APPROVE' if len(violations) == 0 else 'REJECT'
            }
            
        except Exception as e:
            logger.error(f"Risk limit check failed: {e}")
            return {
                'approved': False,
                'violations': [f"Risk check error: {str(e)}"],
                'warnings': [],
                'recommended_action': 'REJECT'
            }

    async def optimize_position_size(self, symbol: str, target_price: float, 
                                   stop_loss: float, risk_amount: float = None) -> Dict[str, Any]:
        """Optimize position size based on risk parameters"""
        try:
            if risk_amount is None:
                risk_amount = self.portfolio_value * self.max_position_size
            
            # Calculate risk per share
            risk_per_share = abs(target_price - stop_loss)
            
            if risk_per_share <= 0:
                return {
                    'recommended_shares': 0,
                    'position_value': 0,
                    'risk_amount': 0,
                    'error': 'Invalid stop loss price'
                }
            
            # Calculate optimal position size
            recommended_shares = int(risk_amount / risk_per_share)
            position_value = recommended_shares * target_price
            position_size_percent = (position_value / self.portfolio_value) * 100
            
            return {
                'recommended_shares': recommended_shares,
                'position_value': position_value,
                'position_size_percent': position_size_percent,
                'risk_amount': risk_amount,
                'risk_per_share': risk_per_share,
                'max_loss': recommended_shares * risk_per_share
            }
            
        except Exception as e:
            logger.error(f"Position size optimization failed: {e}")
            return {'error': str(e)}

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get risk management summary"""
        return {
            'status': self.status.value,
            'risk_parameters': self.risk_parameters,
            'portfolio_value': self.portfolio_value,
            'daily_pnl': self.daily_pnl,
            'position_count': len(self.positions),
            'monitoring_active': getattr(self, 'monitoring_active', False),
            'max_position_size_percent': self.max_position_size * 100,
            'max_portfolio_risk_percent': self.max_portfolio_risk * 100
        }


# ============================================================================
# PORTFOLIO OPTIMIZER
# ============================================================================

class AtlasPortfolioOptimizer:
    """Portfolio optimization engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        logger.info("[UP] Portfolio Optimizer initialized - enabled: True")

    async def initialize(self):
        """Initialize portfolio optimizer"""
        self.status = EngineStatus.ACTIVE
        logger.info("[OK] Portfolio Optimizer ready")

    async def optimize_portfolio(self, symbols: List[str], target_return: float = 0.10) -> Dict[str, Any]:
        """Optimize portfolio allocation"""
        try:
            # Simplified optimization - equal weight for now
            num_symbols = len(symbols)
            if num_symbols == 0:
                return {'error': 'No symbols provided'}
            
            weight_per_symbol = 1.0 / num_symbols
            
            allocation = {}
            for symbol in symbols:
                allocation[symbol] = {
                    'weight': weight_per_symbol,
                    'weight_percent': weight_per_symbol * 100
                }
            
            return {
                'allocation': allocation,
                'expected_return': target_return,
                'expected_risk': 0.15,  # Simplified
                'sharpe_ratio': target_return / 0.15,
                'optimization_method': 'equal_weight'
            }
            
        except Exception as e:
            logger.error(f"Portfolio optimization failed: {e}")
            return {'error': str(e)}


# ============================================================================
# VAR CALCULATOR
# ============================================================================

class AtlasVaRCalculator:
    """Value at Risk calculator"""
    
    def __init__(self):
        self.confidence_levels = [0.95, 0.99]
        logger.info("[CALC] VaR Calculator initialized")

    async def calculate_var(self, portfolio_value: float, volatility: float = 0.15, 
                          confidence: float = 0.95, time_horizon: int = 1) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        try:
            # Simplified VaR calculation using normal distribution
            from scipy.stats import norm
            
            # Calculate VaR
            z_score = norm.ppf(confidence)
            daily_volatility = volatility / np.sqrt(252)
            var_amount = portfolio_value * daily_volatility * z_score * np.sqrt(time_horizon)
            
            return {
                'var_amount': var_amount,
                'var_percent': (var_amount / portfolio_value) * 100,
                'confidence': confidence,
                'time_horizon_days': time_horizon,
                'portfolio_value': portfolio_value,
                'volatility': volatility
            }
            
        except Exception as e:
            logger.error(f"VaR calculation failed: {e}")
            # Fallback calculation
            var_amount = portfolio_value * 0.02  # 2% simple estimate
            return {
                'var_amount': var_amount,
                'var_percent': 2.0,
                'confidence': confidence,
                'time_horizon_days': time_horizon,
                'portfolio_value': portfolio_value,
                'method': 'simplified'
            }


# ============================================================================
# OPTIONS ENGINE
# ============================================================================

class AtlasOptionsEngine:
    """Options trading and analysis engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        logger.info("[TARGET] Options Engine initialized - enabled: True")

    async def initialize(self):
        """Initialize options engine"""
        self.status = EngineStatus.ACTIVE
        logger.info("[OK] Options Engine ready")

    async def calculate_option_price(self, underlying_price: float, strike_price: float,
                                   time_to_expiry: float, volatility: float = 0.25,
                                   risk_free_rate: float = 0.05, option_type: str = 'call') -> Dict[str, Any]:
        """Calculate option price using Black-Scholes"""
        try:
            # Simplified Black-Scholes calculation
            import math
            
            d1 = (math.log(underlying_price / strike_price) + 
                  (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
            d2 = d1 - volatility * math.sqrt(time_to_expiry)
            
            # Approximate normal CDF
            def norm_cdf(x):
                return 0.5 * (1 + math.erf(x / math.sqrt(2)))
            
            if option_type.lower() == 'call':
                price = (underlying_price * norm_cdf(d1) - 
                        strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(d2))
            else:  # put
                price = (strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(-d2) - 
                        underlying_price * norm_cdf(-d1))
            
            return {
                'option_price': max(0, price),
                'delta': norm_cdf(d1) if option_type.lower() == 'call' else norm_cdf(d1) - 1,
                'gamma': math.exp(-d1**2/2) / (underlying_price * volatility * math.sqrt(2 * math.pi * time_to_expiry)),
                'theta': -0.01,  # Simplified
                'vega': underlying_price * math.sqrt(time_to_expiry) * math.exp(-d1**2/2) / math.sqrt(2 * math.pi),
                'rho': 0.01  # Simplified
            }
            
        except Exception as e:
            logger.error(f"Option price calculation failed: {e}")
            return {
                'option_price': 0.0,
                'error': str(e)
            }


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasRiskEngine",
    "AtlasPortfolioOptimizer",
    "AtlasVaRCalculator", 
    "AtlasOptionsEngine"
]
