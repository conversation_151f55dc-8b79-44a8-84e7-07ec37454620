#!/usr/bin/env python3
"""
Test script for functional progress tracking and optimized response flow
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_ai_core import AtlasConversationalEngine, ProgressTracker

async def test_progress_tracking():
    """Test the new progress tracking functionality"""
    print("🧪 Testing A.T.L.A.S. Functional Progress Tracking")
    print("=" * 60)
    
    # Initialize AI core
    ai_core = AtlasConversationalEngine()
    
    # Test cases
    test_cases = [
        {
            "message": "hello",
            "description": "Simple greeting (should use fast path)",
            "expected_simple": True
        },
        {
            "message": "what is a stock?",
            "description": "Educational query (should use fast path)",
            "expected_simple": True
        },
        {
            "message": "analyze AAPL",
            "description": "Stock analysis (should use complex path)",
            "expected_simple": False
        },
        {
            "message": "scan the market for profitable trades",
            "description": "Market scanning (should use complex path)",
            "expected_simple": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['description']}")
        print(f"Message: '{test_case['message']}'")
        
        # Test intent analysis and complexity detection
        intent_analysis = await ai_core._analyze_intent(test_case['message'])
        is_simple = ai_core._is_simple_query(test_case['message'], intent_analysis)
        
        print(f"Intent Type: {intent_analysis.get('type', 'general')}")
        print(f"Detected as Simple: {is_simple}")
        print(f"Expected Simple: {test_case['expected_simple']}")
        
        # Verify complexity detection
        if is_simple == test_case['expected_simple']:
            print("✅ Complexity detection CORRECT")
        else:
            print("❌ Complexity detection INCORRECT")
        
        # Test progress tracking
        session_id = f"test_session_{i}"
        progress_id = f"test_progress_{i}"
        
        print(f"\n📊 Testing Progress Tracking...")
        progress_tracker = ProgressTracker(progress_id, session_id)
        
        # Simulate progress updates
        await progress_tracker.update_step("intent_analysis", "in_progress", 
                                          "Analyzing intent...", "Determining query type")
        await progress_tracker.update_step("intent_analysis", "completed", 
                                          "Intent Analysis Complete ✅", f"Detected: {intent_analysis.get('type')}")
        
        if is_simple:
            await progress_tracker.update_step("routing", "completed", 
                                              "Fast Path Selected ⚡", "Using lightweight pipeline")
        else:
            await progress_tracker.update_step("routing", "completed", 
                                              "Full Pipeline Selected 🔄", "Using comprehensive analysis")
        
        # Get progress data
        progress_data = progress_tracker.get_progress_data()
        print(f"Overall Progress: {progress_data['overall_progress']:.1%}")
        print(f"Active Steps: {len([s for s in progress_data['steps'] if s['status'] == 'completed'])}/{len(progress_data['steps'])}")
        
        print("-" * 40)

async def test_full_processing():
    """Test full message processing with progress tracking"""
    print("\n🚀 Testing Full Message Processing")
    print("=" * 60)
    
    ai_core = AtlasConversationalEngine()
    
    # Test simple query
    print("\n📱 Testing Simple Query Processing...")
    response = await ai_core.process_message(
        message="hello",
        session_id="test_simple",
        enable_progress_tracking=True,
        progress_id="test_simple_progress"
    )
    
    print(f"Response Type: {response.type}")
    print(f"Confidence: {response.confidence}")
    print(f"Response: {response.response[:100]}...")
    
    # Test complex query (without orchestrator for now)
    print("\n🔧 Testing Complex Query Processing...")
    response = await ai_core.process_message(
        message="what are the best trading opportunities today?",
        session_id="test_complex",
        enable_progress_tracking=True,
        progress_id="test_complex_progress"
    )
    
    print(f"Response Type: {response.type}")
    print(f"Confidence: {response.confidence}")
    print(f"Response: {response.response[:100]}...")

if __name__ == "__main__":
    print("🎯 A.T.L.A.S. Progress Tracking & Response Flow Test")
    print("Testing functional progress bars and optimized response routing")
    print()
    
    asyncio.run(test_progress_tracking())
    asyncio.run(test_full_processing())
    
    print("\n✅ Testing Complete!")
    print("Progress tracking and response flow optimization implemented successfully!")
