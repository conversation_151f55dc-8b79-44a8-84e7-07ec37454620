{"market_hours": {"local_time": "2025-07-18T10:46:38.201480", "et_time": "2025-07-18T11:46:38.250683-04:00", "market_hours_broken": true, "market_hours_correct": true, "scanner_should_scan": true}, "data_sources": {"AAPL": {"market_engine": true, "price": 211.0, "change": 0.030000000000001137, "historical_data": true, "latest_close": 210.92, "latest_date": "2025-07-18 00:00:00"}, "MSFT": {"market_engine": true, "price": 510.4, "change": -0.10210000000000719, "historical_data": true, "latest_close": 510.6417, "latest_date": "2025-07-18 00:00:00"}, "GOOGL": {"market_engine": true, "price": 185.03, "change": -0.10499999999998977, "historical_data": true, "latest_close": 185.095, "latest_date": "2025-07-18 00:00:00"}}, "pattern_detection": {"AAPL": {"pattern_found": true, "signal_type": "bullish_momentum", "confidence": 0.9, "strength": "strong", "entry_price": 211.0, "target_price": 219.44, "stop_loss": 206.78}, "MSFT": {"pattern_found": true, "signal_type": "bullish_momentum", "confidence": 0.9, "strength": "strong", "entry_price": 510.5, "target_price": 530.9200000000001, "stop_loss": 500.28999999999996}, "TSLA": {"pattern_found": true, "signal_type": "bullish_momentum", "confidence": 0.75, "strength": "strong", "entry_price": 328.03, "target_price": 341.15119999999996, "stop_loss": 321.46939999999995}}, "pattern_sensitivity": {"Current (Flexible)": {"pattern_found": true, "confidence": 0.9, "strength": "strong"}, "Moderate": {"pattern_found": true, "confidence": 0.9, "strength": "strong"}, "Strict": {"pattern_found": false}}, "scanner_status": {"running": false, "market_hours": true, "scan_count": 0, "last_scan_time": null, "active_results_count": 0, "symbols_monitored": 0, "priority_symbols": 0, "regular_symbols": 0, "api_calls_per_minute": 0, "performance": {"priority_scan_frequency": "Every 8s", "regular_scan_frequency": "Every 12s", "symbols_per_minute": 0, "concurrent_scans": 3, "api_rate_limit": 25, "optimization_status": "OPTIMIZED - Fast scanning enabled"}, "config": {"enabled": true, "scan_interval": 12, "market_hours_only": true, "symbol_filter": null, "min_confidence": 0.6, "max_concurrent_scans": 3, "api_rate_limit": 25, "require_squeeze": false, "pattern_sensitivity": 0.7, "priority_symbols": null, "priority_scan_interval": 8}}}