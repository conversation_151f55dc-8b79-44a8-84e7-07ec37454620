"""
A.T.L.A.S. Multimodal Processing Integration Test
Test suite for multimodal data processing capabilities (v5.0 Phase 2)
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any
import base64

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Import test components
try:
    from atlas_video_processor import AtlasVideoProcessor, VideoContentType
    from atlas_image_analyzer import AtlasImageAnalyzer, ImageType
    from atlas_alternative_data import AtlasAlternativeDataEngine, DataSourceType
    from atlas_data_fusion import AtlasDataFusionEngine, MultimodalInput, ModalityType
    from atlas_ai_core import AtlasAIEngine
    from atlas_orchestrator import AtlasOrchestrator
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    logger.error(f"Import failed: {e}")
    IMPORTS_SUCCESSFUL = False

class MultimodalProcessingTester:
    """Test suite for multimodal processing capabilities"""
    
    def __init__(self):
        self.test_results = {}
        self.orchestrator = None
        self.ai_engine = None
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all multimodal processing tests"""
        logger.info("=" * 60)
        logger.info("A.T.L.A.S. MULTIMODAL PROCESSING TEST SUITE")
        logger.info("=" * 60)
        
        if not IMPORTS_SUCCESSFUL:
            return {
                'success': False,
                'error': 'Failed to import required modules',
                'timestamp': datetime.now().isoformat()
            }
        
        # Test individual components
        await self.test_video_processor()
        await self.test_image_analyzer()
        await self.test_alternative_data_engine()
        await self.test_data_fusion_engine()
        await self.test_ai_engine_integration()
        await self.test_orchestrator_integration()
        
        # Generate summary
        return self.generate_test_summary()
    
    async def test_video_processor(self):
        """Test video processing engine"""
        logger.info("\n[TEST] Video Processing Engine")
        logger.info("-" * 40)
        
        try:
            # Initialize engine
            engine = AtlasVideoProcessor()
            await engine.initialize()
            
            # Test video content processing
            result = await engine.process_video_content(
                "test_earnings_call.mp4", VideoContentType.EARNINGS_CALL
            )
            
            # Validate results
            success = (
                hasattr(result, 'video_id') and
                hasattr(result, 'transcript') and
                hasattr(result, 'sentiment_analysis') and
                hasattr(result, 'market_impact_score')
            )
            
            self.test_results['video_processor'] = {
                'success': success,
                'engine_status': engine.get_engine_status(),
                'sample_result': {
                    'video_id': result.video_id,
                    'content_type': result.content_type.value,
                    'confidence': result.confidence,
                    'market_impact_score': result.market_impact_score
                },
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Video processor test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Video processor test failed: {e}")
            self.test_results['video_processor'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_image_analyzer(self):
        """Test image analysis engine"""
        logger.info("\n[TEST] Image Analysis Engine")
        logger.info("-" * 40)
        
        try:
            # Initialize engine
            engine = AtlasImageAnalyzer()
            await engine.initialize()
            
            # Create dummy image data
            dummy_image_data = b"dummy_chart_image_data"
            
            # Test image analysis
            result = await engine.analyze_image(dummy_image_data, ImageType.STOCK_CHART)
            
            # Validate results
            success = (
                hasattr(result, 'image_id') and
                hasattr(result, 'detected_patterns') and
                hasattr(result, 'trend_direction') and
                hasattr(result, 'sentiment_score')
            )
            
            self.test_results['image_analyzer'] = {
                'success': success,
                'engine_status': engine.get_engine_status(),
                'sample_result': {
                    'image_id': result.image_id,
                    'image_type': result.image_type.value,
                    'detected_patterns': [p.value for p in result.detected_patterns],
                    'trend_direction': result.trend_direction.value,
                    'confidence': result.confidence
                },
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Image analyzer test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Image analyzer test failed: {e}")
            self.test_results['image_analyzer'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_alternative_data_engine(self):
        """Test alternative data engine"""
        logger.info("\n[TEST] Alternative Data Engine")
        logger.info("-" * 40)
        
        try:
            # Initialize engine
            engine = AtlasAlternativeDataEngine()
            await engine.initialize()
            
            # Test data retrieval
            data_points = await engine.get_alternative_data(
                DataSourceType.SOCIAL_MEDIA, 'AAPL', 24
            )
            
            # Test market insights
            insights = await engine.get_market_insights('AAPL', 24)
            
            # Test correlation analysis
            correlation_result = await engine.analyze_market_correlation(
                'AAPL', [DataSourceType.SOCIAL_MEDIA, DataSourceType.SATELLITE_IMAGERY]
            )
            
            # Validate results
            success = (
                isinstance(data_points, list) and
                isinstance(insights, list) and
                'correlations' in correlation_result
            )
            
            self.test_results['alternative_data_engine'] = {
                'success': success,
                'engine_status': engine.get_engine_status(),
                'data_points_count': len(data_points),
                'insights_count': len(insights),
                'correlation_analysis': correlation_result,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Alternative data engine test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Alternative data engine test failed: {e}")
            self.test_results['alternative_data_engine'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_data_fusion_engine(self):
        """Test data fusion engine"""
        logger.info("\n[TEST] Data Fusion Engine")
        logger.info("-" * 40)
        
        try:
            # Initialize engine
            engine = AtlasDataFusionEngine()
            await engine.initialize()
            
            # Create multimodal inputs
            multimodal_inputs = [
                MultimodalInput(
                    modality=ModalityType.TEXT,
                    data="Positive earnings outlook with strong growth",
                    timestamp=datetime.now(),
                    confidence=0.8,
                    metadata={'source': 'test'},
                    source_id='text_001'
                ),
                MultimodalInput(
                    modality=ModalityType.NUMERICAL,
                    data={'price_change': 0.05, 'volume_ratio': 1.5},
                    timestamp=datetime.now(),
                    confidence=0.9,
                    metadata={'source': 'test'},
                    source_id='num_001'
                )
            ]
            
            # Test multimodal fusion
            fusion_result = await engine.fuse_multimodal_data(multimodal_inputs, 'AAPL')
            
            # Test cross-modal correlations
            correlations = await engine.analyze_cross_modal_correlations('AAPL', 24)
            
            # Validate results
            success = (
                hasattr(fusion_result, 'fusion_id') and
                hasattr(fusion_result, 'market_prediction') and
                hasattr(fusion_result, 'confidence_score') and
                isinstance(correlations, list)
            )
            
            self.test_results['data_fusion_engine'] = {
                'success': success,
                'engine_status': engine.get_engine_status(),
                'fusion_result': {
                    'fusion_id': fusion_result.fusion_id,
                    'input_modalities': [m.value for m in fusion_result.input_modalities],
                    'confidence_score': fusion_result.confidence_score,
                    'market_prediction': fusion_result.market_prediction
                },
                'correlations_count': len(correlations),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Data fusion engine test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Data fusion engine test failed: {e}")
            self.test_results['data_fusion_engine'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_ai_engine_integration(self):
        """Test AI engine integration with multimodal components"""
        logger.info("\n[TEST] AI Engine Multimodal Integration")
        logger.info("-" * 40)
        
        try:
            # Initialize AI engine
            self.ai_engine = AtlasAIEngine()
            await self.ai_engine.initialize()
            
            # Test multimodal status
            multimodal_status = self.ai_engine.get_multimodal_status()
            
            # Test video processing through AI engine
            video_result = await self.ai_engine.process_video_content(
                "test_video.mp4", "earnings_call"
            )
            
            # Test image analysis through AI engine
            dummy_image = base64.b64encode(b"dummy_image_data").decode()
            image_result = await self.ai_engine.analyze_chart_image(
                base64.b64decode(dummy_image)
            )
            
            # Test alternative data through AI engine
            alt_data_result = await self.ai_engine.get_alternative_market_data(
                "social_media", "AAPL"
            )
            
            # Validate results
            success = (
                multimodal_status['multimodal_available'] and
                'video_analysis' in video_result and
                'image_analysis' in image_result and
                'data_points' in alt_data_result
            )
            
            self.test_results['ai_engine_multimodal'] = {
                'success': success,
                'multimodal_available': multimodal_status['multimodal_available'],
                'components_count': len(multimodal_status.get('components', {})),
                'video_processing_working': 'video_analysis' in video_result,
                'image_analysis_working': 'image_analysis' in image_result,
                'alt_data_working': 'data_points' in alt_data_result,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ AI engine multimodal integration test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ AI engine multimodal integration test failed: {e}")
            self.test_results['ai_engine_multimodal'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_orchestrator_integration(self):
        """Test orchestrator integration with multimodal processing"""
        logger.info("\n[TEST] Orchestrator Multimodal Integration")
        logger.info("-" * 40)
        
        try:
            # Initialize orchestrator
            self.orchestrator = AtlasOrchestrator()
            await self.orchestrator.initialize()
            
            # Test system status with multimodal components
            system_status = await self.orchestrator.get_system_status()
            
            # Test multimodal methods through orchestrator
            video_result = await self.orchestrator.process_video_content(
                "test_video.mp4", "earnings_call"
            )
            
            dummy_image = base64.b64encode(b"dummy_image_data").decode()
            image_result = await self.orchestrator.analyze_chart_image(
                base64.b64decode(dummy_image)
            )
            
            alt_data_result = await self.orchestrator.get_alternative_market_data(
                "social_media", "AAPL"
            )
            
            fusion_result = await self.orchestrator.fuse_multimodal_analysis(
                "AAPL", "test_video.mp4", base64.b64decode(dummy_image), ["social_media"]
            )
            
            # Validate results
            success = (
                'multimodal_processing' in system_status and
                video_result.get('success', False) and
                image_result.get('success', False) and
                alt_data_result.get('success', False) and
                fusion_result.get('success', False)
            )
            
            self.test_results['orchestrator_multimodal'] = {
                'success': success,
                'system_status': system_status.get('orchestrator_status'),
                'multimodal_integrated': 'multimodal_processing' in system_status,
                'video_processing_accessible': video_result.get('success', False),
                'image_analysis_accessible': image_result.get('success', False),
                'alt_data_accessible': alt_data_result.get('success', False),
                'fusion_accessible': fusion_result.get('success', False),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Orchestrator multimodal integration test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Orchestrator multimodal integration test failed: {e}")
            self.test_results['orchestrator_multimodal'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def generate_test_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Detailed results
        for test_name, result in self.test_results.items():
            status = "✓ PASSED" if result.get('success', False) else "✗ FAILED"
            logger.info(f"{test_name}: {status}")
            if not result.get('success', False) and 'error' in result:
                logger.info(f"  Error: {result['error']}")
        
        return {
            'success': passed_tests == total_tests,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': (passed_tests/total_tests)*100 if total_tests > 0 else 0,
            'detailed_results': self.test_results,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main test execution"""
    tester = MultimodalProcessingTester()
    results = await tester.run_all_tests()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    print(f"Overall Success: {results['success']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")
    print(f"Tests Passed: {results['passed_tests']}/{results['total_tests']}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
