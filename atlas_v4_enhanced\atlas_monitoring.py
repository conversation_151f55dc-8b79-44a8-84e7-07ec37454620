"""
A.T.L.A.S Monitoring - Consolidated System Monitoring and Metrics
Combines Proactive Assistant, Guru Scoring Metrics, and Market Context
"""

import asyncio
import logging
import json
import sys
import os
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

logger = logging.getLogger(__name__)


# ============================================================================
# SYSTEM MONITORING
# ============================================================================

class AtlasSystemMonitor:
    """System performance and health monitoring"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.metrics_history = []
        self.alerts = []
        self.monitoring_enabled = True
        
        logger.info("[MONITOR] System Monitor initialized")

    async def initialize(self):
        """Initialize system monitoring"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize monitoring thresholds
            self.thresholds = {
                'cpu_usage': 80.0,  # %
                'memory_usage': 85.0,  # %
                'disk_usage': 90.0,  # %
                'response_time': 5.0,  # seconds
                'error_rate': 5.0  # %
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] System Monitor ready")
            
        except Exception as e:
            logger.error(f"System Monitor initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect current system metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            
            # Network metrics (if available)
            try:
                network = psutil.net_io_counters()
                bytes_sent = network.bytes_sent
                bytes_recv = network.bytes_recv
            except:
                bytes_sent = bytes_recv = 0
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'core_count': cpu_count,
                    'status': 'normal' if cpu_percent < self.thresholds['cpu_usage'] else 'high'
                },
                'memory': {
                    'usage_percent': memory_percent,
                    'available_gb': round(memory_available, 2),
                    'total_gb': round(memory.total / (1024**3), 2),
                    'status': 'normal' if memory_percent < self.thresholds['memory_usage'] else 'high'
                },
                'disk': {
                    'usage_percent': disk_percent,
                    'free_gb': round(disk_free, 2),
                    'total_gb': round(disk.total / (1024**3), 2),
                    'status': 'normal' if disk_percent < self.thresholds['disk_usage'] else 'high'
                },
                'network': {
                    'bytes_sent': bytes_sent,
                    'bytes_received': bytes_recv
                }
            }
            
            # Store metrics history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > 1000:  # Keep last 1000 entries
                self.metrics_history = self.metrics_history[-1000:]
            
            # Check for alerts
            await self._check_system_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"System metrics collection failed: {e}")
            return {'error': str(e)}

    async def _check_system_alerts(self, metrics: Dict[str, Any]):
        """Check for system alerts based on metrics"""
        try:
            alerts = []
            
            # CPU alert
            if metrics['cpu']['usage_percent'] > self.thresholds['cpu_usage']:
                alerts.append({
                    'type': 'high_cpu',
                    'message': f"High CPU usage: {metrics['cpu']['usage_percent']:.1f}%",
                    'severity': 'warning',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Memory alert
            if metrics['memory']['usage_percent'] > self.thresholds['memory_usage']:
                alerts.append({
                    'type': 'high_memory',
                    'message': f"High memory usage: {metrics['memory']['usage_percent']:.1f}%",
                    'severity': 'warning',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Disk alert
            if metrics['disk']['usage_percent'] > self.thresholds['disk_usage']:
                alerts.append({
                    'type': 'high_disk',
                    'message': f"High disk usage: {metrics['disk']['usage_percent']:.1f}%",
                    'severity': 'critical',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Add alerts to history
            self.alerts.extend(alerts)
            if len(self.alerts) > 100:  # Keep last 100 alerts
                self.alerts = self.alerts[-100:]
            
            # Log critical alerts
            for alert in alerts:
                if alert['severity'] == 'critical':
                    logger.warning(f"[ALERT] {alert['message']}")
                    
        except Exception as e:
            logger.error(f"System alert check failed: {e}")

    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            if not self.metrics_history:
                return {'status': 'unknown', 'message': 'No metrics available'}
            
            latest_metrics = self.metrics_history[-1]
            
            # Calculate health score
            health_score = 100
            
            if latest_metrics['cpu']['status'] == 'high':
                health_score -= 20
            if latest_metrics['memory']['status'] == 'high':
                health_score -= 25
            if latest_metrics['disk']['status'] == 'high':
                health_score -= 30
            
            # Recent alerts impact
            recent_alerts = [
                alert for alert in self.alerts
                if (datetime.now() - datetime.fromisoformat(alert['timestamp'])).minutes < 30
            ]
            health_score -= len(recent_alerts) * 5
            
            # Determine status
            if health_score >= 90:
                status = 'excellent'
            elif health_score >= 70:
                status = 'good'
            elif health_score >= 50:
                status = 'fair'
            else:
                status = 'poor'
            
            return {
                'status': status,
                'health_score': max(0, health_score),
                'latest_metrics': latest_metrics,
                'recent_alerts': len(recent_alerts),
                'monitoring_enabled': self.monitoring_enabled
            }
            
        except Exception as e:
            logger.error(f"System health check failed: {e}")
            return {'status': 'error', 'error': str(e)}


# ============================================================================
# PROACTIVE ASSISTANT
# ============================================================================

class AtlasProactiveAssistant:
    """Proactive trading assistant with intelligent suggestions"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.suggestions = []
        self.user_context = {}
        self.market_context = {}
        
        logger.info("[BOT] Proactive Assistant initialized")

    async def initialize(self):
        """Initialize proactive assistant"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize suggestion templates
            self.suggestion_templates = {
                'market_close': "Market is closing soon - would you like a summary of today's activity?",
                'portfolio_review': "It's been a week since your last portfolio review - shall we analyze performance?",
                'risk_check': "Your portfolio risk has increased - would you like me to suggest adjustments?",
                'news_alert': "Important news detected for your watchlist - would you like an analysis?",
                'learning_opportunity': "Based on recent trades, here's a learning opportunity for you."
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Proactive Assistant ready")
            
        except Exception as e:
            logger.error(f"Proactive Assistant initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def generate_suggestions(self, user_context: Dict[str, Any] = None, 
                                 market_context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Generate proactive suggestions"""
        try:
            suggestions = []
            current_time = datetime.now()
            
            # Update context
            if user_context:
                self.user_context.update(user_context)
            if market_context:
                self.market_context.update(market_context)
            
            # Time-based suggestions
            if 15 <= current_time.hour <= 16:  # Market closing time
                suggestions.append({
                    'type': 'market_close',
                    'message': self.suggestion_templates['market_close'],
                    'priority': 'medium',
                    'timestamp': current_time.isoformat()
                })
            
            # Portfolio-based suggestions
            if self.user_context.get('portfolio_value'):
                last_review = self.user_context.get('last_portfolio_review')
                if not last_review or (current_time - datetime.fromisoformat(last_review)).days >= 7:
                    suggestions.append({
                        'type': 'portfolio_review',
                        'message': self.suggestion_templates['portfolio_review'],
                        'priority': 'low',
                        'timestamp': current_time.isoformat()
                    })
            
            # Risk-based suggestions
            portfolio_risk = self.user_context.get('portfolio_risk', 0)
            if portfolio_risk > 0.15:  # 15% risk threshold
                suggestions.append({
                    'type': 'risk_check',
                    'message': self.suggestion_templates['risk_check'],
                    'priority': 'high',
                    'timestamp': current_time.isoformat()
                })
            
            # Market-based suggestions
            if self.market_context.get('volatility', 0) > 0.25:  # High volatility
                suggestions.append({
                    'type': 'volatility_alert',
                    'message': "Market volatility is elevated - consider reviewing your positions.",
                    'priority': 'medium',
                    'timestamp': current_time.isoformat()
                })
            
            # Learning suggestions for beginners
            if self.user_context.get('experience_level') == 'beginner':
                suggestions.append({
                    'type': 'learning_opportunity',
                    'message': self.suggestion_templates['learning_opportunity'],
                    'priority': 'low',
                    'timestamp': current_time.isoformat()
                })
            
            # Store suggestions
            self.suggestions.extend(suggestions)
            if len(self.suggestions) > 50:  # Keep last 50 suggestions
                self.suggestions = self.suggestions[-50:]
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Suggestion generation failed: {e}")
            return []

    def get_recent_suggestions(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent suggestions"""
        return self.suggestions[-limit:] if self.suggestions else []


# ============================================================================
# GURU SCORING METRICS
# ============================================================================

class AtlasGuruScoringMetrics:
    """Advanced scoring metrics for trading performance"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.performance_history = []
        self.scoring_weights = {
            'win_rate': 0.25,
            'profit_factor': 0.25,
            'sharpe_ratio': 0.20,
            'max_drawdown': 0.15,
            'consistency': 0.15
        }
        
        logger.info("[GURU] Guru Scoring Metrics initialized")

    async def initialize(self):
        """Initialize guru scoring system"""
        try:
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Guru Scoring Metrics ready")
            
        except Exception as e:
            logger.error(f"Guru Scoring initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def calculate_guru_score(self, trading_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive guru score"""
        try:
            # Extract performance metrics
            total_trades = trading_performance.get('total_trades', 0)
            winning_trades = trading_performance.get('winning_trades', 0)
            total_profit = trading_performance.get('total_profit', 0)
            total_loss = trading_performance.get('total_loss', 0)
            max_drawdown = trading_performance.get('max_drawdown', 0)
            
            if total_trades == 0:
                return {
                    'guru_score': 0,
                    'grade': 'N/A',
                    'message': 'No trading history available'
                }
            
            # Calculate individual metrics
            win_rate = (winning_trades / total_trades) * 100
            profit_factor = abs(total_profit / total_loss) if total_loss != 0 else 0
            
            # Simplified Sharpe ratio calculation
            avg_return = (total_profit + total_loss) / total_trades if total_trades > 0 else 0
            return_std = abs(avg_return) * 0.5  # Simplified volatility
            sharpe_ratio = avg_return / return_std if return_std != 0 else 0
            
            # Consistency score (simplified)
            consistency = min(100, win_rate * 1.2) if win_rate > 0 else 0
            
            # Calculate weighted score
            scores = {
                'win_rate': min(100, win_rate * 2),  # Scale to 0-100
                'profit_factor': min(100, profit_factor * 50),  # Scale to 0-100
                'sharpe_ratio': min(100, (sharpe_ratio + 2) * 25),  # Scale to 0-100
                'max_drawdown': max(0, 100 - (max_drawdown * 100 * 5)),  # Penalty for drawdown
                'consistency': consistency
            }
            
            # Calculate weighted guru score
            guru_score = sum(
                scores[metric] * weight
                for metric, weight in self.scoring_weights.items()
            )
            
            # Determine grade
            if guru_score >= 90:
                grade = 'A+'
            elif guru_score >= 80:
                grade = 'A'
            elif guru_score >= 70:
                grade = 'B'
            elif guru_score >= 60:
                grade = 'C'
            elif guru_score >= 50:
                grade = 'D'
            else:
                grade = 'F'
            
            return {
                'guru_score': round(guru_score, 1),
                'grade': grade,
                'individual_scores': scores,
                'metrics': {
                    'win_rate': round(win_rate, 1),
                    'profit_factor': round(profit_factor, 2),
                    'sharpe_ratio': round(sharpe_ratio, 2),
                    'max_drawdown': round(max_drawdown * 100, 1),
                    'consistency': round(consistency, 1)
                },
                'total_trades': total_trades,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Guru score calculation failed: {e}")
            return {'error': str(e)}


# ============================================================================
# MARKET CONTEXT ANALYZER
# ============================================================================

class AtlasMarketContextAnalyzer:
    """Market context and regime analysis"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.market_regimes = ['bull', 'bear', 'sideways', 'volatile']
        self.context_history = []
        
        logger.info("[CONTEXT] Market Context Analyzer initialized")

    async def initialize(self):
        """Initialize market context analyzer"""
        try:
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Market Context Analyzer ready")
            
        except Exception as e:
            logger.error(f"Market Context Analyzer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def analyze_market_context(self, market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze current market context"""
        try:
            # Simulate market context analysis
            # In production, this would analyze real market data
            
            import random
            
            context = {
                'timestamp': datetime.now().isoformat(),
                'market_regime': random.choice(self.market_regimes),
                'volatility_level': random.choice(['low', 'medium', 'high']),
                'trend_strength': random.uniform(0.3, 0.9),
                'sector_rotation': random.choice(['growth', 'value', 'defensive']),
                'risk_sentiment': random.choice(['risk_on', 'risk_off', 'neutral']),
                'key_levels': {
                    'support': 4200,
                    'resistance': 4400,
                    'current': 4300
                },
                'economic_indicators': {
                    'inflation_trend': 'declining',
                    'employment_trend': 'stable',
                    'gdp_growth': 'moderate'
                }
            }
            
            # Store context history
            self.context_history.append(context)
            if len(self.context_history) > 100:
                self.context_history = self.context_history[-100:]
            
            return context
            
        except Exception as e:
            logger.error(f"Market context analysis failed: {e}")
            return {'error': str(e)}


# ============================================================================
# MONITORING ORCHESTRATOR
# ============================================================================

class AtlasMonitoringOrchestrator:
    """Main monitoring orchestrator"""
    
    def __init__(self):
        self.system_monitor = AtlasSystemMonitor()
        self.proactive_assistant = AtlasProactiveAssistant()
        self.guru_scoring = AtlasGuruScoringMetrics()
        self.market_context = AtlasMarketContextAnalyzer()
        self.status = EngineStatus.INITIALIZING
        
        logger.info("[ORCHESTRATOR] Monitoring Orchestrator initialized")

    async def initialize(self):
        """Initialize all monitoring components"""
        try:
            await self.system_monitor.initialize()
            await self.proactive_assistant.initialize()
            await self.guru_scoring.initialize()
            await self.market_context.initialize()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Monitoring Orchestrator fully initialized")
            
        except Exception as e:
            logger.error(f"Monitoring Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def comprehensive_monitoring_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        try:
            # Collect all monitoring data
            system_metrics = await self.system_monitor.collect_system_metrics()
            system_health = self.system_monitor.get_system_health()
            suggestions = await self.proactive_assistant.generate_suggestions()
            market_context = await self.market_context.analyze_market_context()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'system_metrics': system_metrics,
                'system_health': system_health,
                'proactive_suggestions': suggestions,
                'market_context': market_context,
                'monitoring_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"Comprehensive monitoring report failed: {e}")
            return {'error': str(e)}


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasSystemMonitor",
    "AtlasProactiveAssistant",
    "AtlasGuruScoringMetrics",
    "AtlasMarketContextAnalyzer",
    "AtlasMonitoringOrchestrator"
]
